"""Episode servers scraping functionality."""
import requests
from bs4 import <PERSON><PERSON>ou<PERSON>
from typing import Dict, Any
import cloudscraper

from src.management import get_logger
from src.utils.constants import SRC_BASE_URL, SRC_AJAX_URL
from src.utils.config import Config
from src.models import EpisodeServer, EpisodeServers

# Configure logging
logger = get_logger("EpisodeServers")

class HiAnimeError(Exception):
    """Custom exception for anime scraping errors."""
    def __init__(self, message: str, context: str, status_code: int):
        super().__init__(message)
        self.context = context
        self.status_code = status_code

    @staticmethod
    def wrapError(err, context):
        """Wrap an error in HiAnimeError."""
        if isinstance(err, HiAnimeError):
            return err
        return HiAnimeError(str(err), context, 500)

def get_episode_servers(episode_id: str) -> EpisodeServers:
    """
    Get episode servers for a given episode ID.
    
    Args:
        episode_id: The episode ID in format 'anime-title?ep=12345'
        
    Returns:
        EpisodeServers: Object containing server information for sub, dub, and raw categories
        
    Raises:
        HiAnimeError: If episode_id is invalid or scraping fails
    """
    # Initialize result object
    result = EpisodeServers(
        sub=[],
        dub=[],
        raw=[],
        episodeId=episode_id,
        episodeNo=0
    )
    
    try:
        # Validate episode_id
        if not episode_id or episode_id.strip() == "" or "?ep=" not in episode_id:
            raise HiAnimeError(
                "invalid anime episode id",
                "get_episode_servers",
                400
            )
        
        # Extract episode ID from the URL parameter
        ep_id = episode_id.split("?ep=")[1]
        
        # Create cloudscraper session for Cloudflare protection
        scraper = cloudscraper.create_scraper()
        
        # Make request to get episode servers
        url = f"{SRC_AJAX_URL}/v2/episode/servers?episodeId={ep_id}"
        headers = {
            "X-Requested-With": "XMLHttpRequest",
            "Referer": f"{SRC_BASE_URL}/watch/{episode_id}",
            **Config.get_headers()
        }
        
        logger.info(f"Fetching episode servers from: {url}")
        response = scraper.get(url, headers=headers, timeout=Config.REQUEST_TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        if "html" not in data:
            raise HiAnimeError(
                "Invalid response format - missing html",
                "get_episode_servers",
                500
            )
        
        # Parse HTML content
        soup = BeautifulSoup(data["html"], 'html.parser')
        
        # Extract episode number
        ep_no_element = soup.select_one(".server-notice strong")
        if ep_no_element:
            ep_no_text = ep_no_element.get_text(strip=True)
            # Extract number from text like "Episode 12"
            ep_no_parts = ep_no_text.split()
            if ep_no_parts:
                try:
                    result.episodeNo = int(ep_no_parts[-1])
                except (ValueError, IndexError):
                    result.episodeNo = 0
        
        # Extract sub servers
        sub_servers = soup.select(".ps_-block.ps_-block-sub.servers-sub .ps__-list .server-item")
        for server_element in sub_servers:
            server_name_element = server_element.select_one("a")
            server_name = server_name_element.get_text(strip=True).lower() if server_name_element else None
            
            server_id_attr = server_element.get("data-server-id")
            server_id = int(server_id_attr.strip()) if server_id_attr and server_id_attr.strip() else None
            
            result.sub.append(EpisodeServer(
                serverName=server_name,
                serverId=server_id
            ))
        
        # Extract dub servers
        dub_servers = soup.select(".ps_-block.ps_-block-sub.servers-dub .ps__-list .server-item")
        for server_element in dub_servers:
            server_name_element = server_element.select_one("a")
            server_name = server_name_element.get_text(strip=True).lower() if server_name_element else None
            
            server_id_attr = server_element.get("data-server-id")
            server_id = int(server_id_attr.strip()) if server_id_attr and server_id_attr.strip() else None
            
            result.dub.append(EpisodeServer(
                serverName=server_name,
                serverId=server_id
            ))
        
        # Extract raw servers
        raw_servers = soup.select(".ps_-block.ps_-block-sub.servers-raw .ps__-list .server-item")
        for server_element in raw_servers:
            server_name_element = server_element.select_one("a")
            server_name = server_name_element.get_text(strip=True).lower() if server_name_element else None
            
            server_id_attr = server_element.get("data-server-id")
            server_id = int(server_id_attr.strip()) if server_id_attr and server_id_attr.strip() else None
            
            result.raw.append(EpisodeServer(
                serverName=server_name,
                serverId=server_id
            ))
        
        logger.info(f"Successfully extracted episode servers for {episode_id}")
        logger.info(f"Found {len(result.sub)} sub servers, {len(result.dub)} dub servers, {len(result.raw)} raw servers")
        
        return result
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Network error while fetching episode servers: {str(e)}")
        raise HiAnimeError.wrapError(e, "get_episode_servers")
    except Exception as err:
        logger.error(f"Error while extracting episode servers: {str(err)}")
        raise HiAnimeError.wrapError(err, "get_episode_servers")

async def get_episode_servers_async(episode_id: str) -> Dict[str, Any]:
    """
    Async wrapper for get_episode_servers that returns a standardized response format.
    
    Args:
        episode_id: The episode ID in format 'anime-title?ep=12345'
        
    Returns:
        Dictionary with success status and data or error information
    """
    try:
        logger.info(f"Getting episode servers for: {episode_id}")
        
        result = get_episode_servers(episode_id)
        
        # Convert to dictionary format for JSON serialization
        return {
            "success": True,
            "data": {
                "sub": [
                    {
                        "serverName": server.serverName,
                        "serverId": server.serverId
                    }
                    for server in result.sub
                ],
                "dub": [
                    {
                        "serverName": server.serverName,
                        "serverId": server.serverId
                    }
                    for server in result.dub
                ],
                "raw": [
                    {
                        "serverName": server.serverName,
                        "serverId": server.serverId
                    }
                    for server in result.raw
                ],
                "episodeId": result.episodeId,
                "episodeNo": result.episodeNo
            }
        }
        
    except HiAnimeError as e:
        logger.error(f"HiAnimeError getting episode servers: {e.context} (Status: {e.status_code})")
        return {
            "success": False,
            "error": str(e),
            "context": e.context,
            "status_code": e.status_code
        }
    except Exception as e:
        logger.error(f"Unexpected error getting episode servers: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }
