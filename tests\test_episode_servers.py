"""Test the episode servers scraper."""
import asyncio
import json
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.management import get_logger
from src.scrapers.episodeServers import get_episode_servers, get_episode_servers_async, HiAnimeError

# Configure logging
logger = get_logger("TestEpisodeServers")

def test_episode_servers_direct():
    """Test the episode servers scraper directly."""
    try:
        logger.info("Testing episode servers scraper directly...")
        
        # Test with a sample episode ID (you may need to adjust this)
        episode_id = "attack-on-titan-112?ep=3303"
        
        result = get_episode_servers(episode_id)
        
        logger.info(f"✓ Successfully retrieved episode servers")
        logger.info(f"  - Episode ID: {result.episodeId}")
        logger.info(f"  - Episode No: {result.episodeNo}")
        logger.info(f"  - Sub servers: {len(result.sub)}")
        logger.info(f"  - Dub servers: {len(result.dub)}")
        logger.info(f"  - Raw servers: {len(result.raw)}")
        
        # Print server details
        if result.sub:
            logger.info("  Sub servers:")
            for i, server in enumerate(result.sub[:3]):  # Show first 3
                logger.info(f"    {i+1}. {server.serverName} (ID: {server.serverId}, HiAnime: {server.hianimeid})")
        
        if result.dub:
            logger.info("  Dub servers:")
            for i, server in enumerate(result.dub[:3]):  # Show first 3
                logger.info(f"    {i+1}. {server.serverName} (ID: {server.serverId}, HiAnime: {server.hianimeid})")
        
        return True
        
    except HiAnimeError as e:
        logger.error(f"✗ HiAnimeError: {e.context} (Status: {e.status_code})")
        return False
    except Exception as e:
        logger.error(f"✗ Direct test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def test_episode_servers_async():
    """Test the async episode servers scraper."""
    try:
        logger.info("Testing async episode servers scraper...")
        
        episode_id = "attack-on-titan-112?ep=3303"
        
        result = await get_episode_servers_async(episode_id)
        
        if result.get("success"):
            data = result["data"]
            logger.info(f"✓ Successfully retrieved episode servers (async)")
            logger.info(f"  - Episode ID: {data['episodeId']}")
            logger.info(f"  - Episode No: {data['episodeNo']}")
            logger.info(f"  - Sub servers: {len(data['sub'])}")
            logger.info(f"  - Dub servers: {len(data['dub'])}")
            logger.info(f"  - Raw servers: {len(data['raw'])}")
            
            # Print some server details
            if data["sub"]:
                logger.info("  First sub server:")
                logger.info(f"    Name: {data['sub'][0]['serverName']}")
                logger.info(f"    ID: {data['sub'][0]['serverId']}")
                logger.info(f"    HiAnime ID: {data['sub'][0]['hianimeid']}")
        else:
            logger.error(f"✗ Async test failed: {result.get('error')}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Async test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def test_episode_servers_invalid_input():
    """Test the episode servers scraper with invalid input."""
    try:
        logger.info("Testing episode servers scraper with invalid input...")
        
        # Test with invalid episode ID
        invalid_ids = [
            "",
            "invalid-id",
            "no-ep-param",
            "missing?param=123"
        ]
        
        for invalid_id in invalid_ids:
            try:
                result = get_episode_servers(invalid_id)
                logger.error(f"✗ Expected error for invalid ID '{invalid_id}' but got result")
                return False
            except HiAnimeError as e:
                logger.info(f"✓ Correctly caught error for '{invalid_id}': {e.context}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Invalid input test failed: {str(e)}")
        return False

async def test_mcp_tool():
    """Test the MCP tool function."""
    try:
        logger.info("Testing MCP tool function...")
        
        # Import the tool function
        from main import get_episode_servers as mcp_get_episode_servers
        
        # Create a mock context (the function doesn't actually use it)
        class MockContext:
            pass
        
        ctx = MockContext()
        episode_id = "attack-on-titan-112?ep=3303"
        
        result = await mcp_get_episode_servers(ctx, episode_id)
        
        if result.get("success"):
            data = result["data"]
            logger.info(f"✓ Successfully called MCP tool")
            logger.info(f"  - Episode ID: {data['episodeId']}")
            logger.info(f"  - Episode No: {data['episodeNo']}")
            logger.info(f"  - Sub servers: {len(data['sub'])}")
            logger.info(f"  - Dub servers: {len(data['dub'])}")
            logger.info(f"  - Raw servers: {len(data['raw'])}")
        else:
            logger.error(f"✗ MCP tool failed: {result.get('error')}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ MCP tool test failed: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def run_all_tests():
    """Run all episode servers tests."""
    logger.info("=" * 50)
    logger.info("Running Episode Servers Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Direct Scraper Test", test_episode_servers_direct),
        ("Async Scraper Test", test_episode_servers_async),
        ("Invalid Input Test", test_episode_servers_invalid_input),
        ("MCP Tool Test", test_mcp_tool),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test '{test_name}' crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("Test Results Summary")
    logger.info("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        logger.info(f"{status}: {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    return passed == len(results)

if __name__ == "__main__":
    asyncio.run(run_all_tests())
